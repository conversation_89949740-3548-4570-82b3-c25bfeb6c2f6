<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海报页面测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .poster-container {
            background-color: #ffffff;
            max-width: 600px;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .case-detail {
            padding: 30px;
        }
        
        .user-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #007AFF;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-size: 16px;
            color: #333;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .publish-time {
            font-size: 14px;
            color: #999;
        }
        
        .case-tags {
            margin-bottom: 15px;
        }
        
        .tag {
            display: inline-block;
            background-color: #f0f8ff;
            color: #1890ff;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 10px;
            margin-right: 8px;
            margin-bottom: 5px;
        }
        
        .case-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin-bottom: 20px;
        }
        
        .case-content {
            font-size: 15px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .case-images {
            margin-bottom: 20px;
        }
        
        .case-image {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .stats-section {
            margin-bottom: 30px;
            padding: 15px 0;
            border-top: 1px solid #f0f0f0;
        }
        
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-label {
            font-size: 14px;
            color: #999;
        }
        
        .qrcode-section {
            text-align: center;
            padding: 30px 0;
            border-top: 2px solid #f0f0f0;
        }
        
        .qrcode-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .qrcode-canvas {
            width: 120px;
            height: 120px;
            margin: 0 auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="poster-container">
        <div class="case-detail">
            <!-- 用户信息 -->
            <div class="user-section">
                <div class="user-avatar">张</div>
                <div class="user-info">
                    <div class="user-name">张三</div>
                    <div class="publish-time">2024-01-15 10:30</div>
                </div>
            </div>

            <!-- 案例标签 -->
            <div class="case-tags">
                <span class="tag">#成功案例</span>
                <span class="tag">#技术分享</span>
                <span class="tag">#经验总结</span>
            </div>

            <!-- 案例标题 -->
            <div class="case-title">如何通过技术创新提升业务效率</div>

            <!-- 案例内容 -->
            <div class="case-content">
                在当今快速发展的数字化时代，技术创新已成为企业提升竞争力的关键因素。本案例将分享我们团队在项目实施过程中的经验和心得，希望能为同行提供有价值的参考。

                通过引入先进的技术架构和优化业务流程，我们成功将项目交付效率提升了40%，同时降低了运营成本。这一成果的取得离不开团队的共同努力和持续的技术探索。
            </div>

            <!-- 案例图片 -->
            <div class="case-images">
                <div style="width: 100%; height: 200px; background-color: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999; margin-bottom: 10px;">
                    案例配图
                </div>
            </div>

            <!-- 点击统计 -->
            <div class="stats-section">
                <div class="stats-item">
                    <span class="stats-label">技术案例</span>
                    <span class="stats-label">阅读156次</span>
                </div>
            </div>

            <!-- 二维码区域 -->
            <div class="qrcode-section">
                <div class="qrcode-title">扫码查看详情</div>
                <div class="qrcode-canvas">
                    二维码区域
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟获取案例ID
        const urlParams = new URLSearchParams(window.location.search);
        const caseId = urlParams.get('caseId') || '1';
        
        console.log('案例ID:', caseId);
        
        // 这里可以添加真实的数据加载逻辑
        // 以及二维码生成逻辑
    </script>
</body>
</html>
