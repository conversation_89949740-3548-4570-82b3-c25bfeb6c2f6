// 简单的二维码生成工具
// 基于QR Code规范的简化实现

class QRCode {
  constructor() {
    this.modules = []
    this.moduleCount = 0
    this.dataList = []
    this.errorCorrectLevel = QRErrorCorrectLevel.L
  }

  addData(data) {
    const newData = new QR8bitByte(data)
    this.dataList.push(newData)
    this.dataCache = null
  }

  make() {
    this.makeImpl(false, this.getBestMaskPattern())
  }

  makeImpl(test, maskPattern) {
    this.moduleCount = this.dataList.length * 4 + 17
    this.modules = new Array(this.moduleCount)
    
    for (let row = 0; row < this.moduleCount; row++) {
      this.modules[row] = new Array(this.moduleCount)
      for (let col = 0; col < this.moduleCount; col++) {
        this.modules[row][col] = null
      }
    }

    this.setupPositionProbePattern(0, 0)
    this.setupPositionProbePattern(this.moduleCount - 7, 0)
    this.setupPositionProbePattern(0, this.moduleCount - 7)
    this.setupPositionAdjustPattern()
    this.setupTimingPattern()
    this.setupTypeInfo(test, maskPattern)
    
    if (this.dataList.length > 0) {
      this.mapData(this.createData(), maskPattern)
    }
  }

  setupPositionProbePattern(row, col) {
    for (let r = -1; r <= 7; r++) {
      if (row + r <= -1 || this.moduleCount <= row + r) continue
      
      for (let c = -1; c <= 7; c++) {
        if (col + c <= -1 || this.moduleCount <= col + c) continue
        
        if ((0 <= r && r <= 6 && (c == 0 || c == 6)) ||
            (0 <= c && c <= 6 && (r == 0 || r == 6)) ||
            (2 <= r && r <= 4 && 2 <= c && c <= 4)) {
          this.modules[row + r][col + c] = true
        } else {
          this.modules[row + r][col + c] = false
        }
      }
    }
  }

  setupTimingPattern() {
    for (let r = 8; r < this.moduleCount - 8; r++) {
      if (this.modules[r][6] != null) continue
      this.modules[r][6] = (r % 2 == 0)
    }

    for (let c = 8; c < this.moduleCount - 8; c++) {
      if (this.modules[6][c] != null) continue
      this.modules[6][c] = (c % 2 == 0)
    }
  }

  setupPositionAdjustPattern() {
    // 简化实现，跳过位置调整图案
  }

  setupTypeInfo(test, maskPattern) {
    const data = (this.errorCorrectLevel << 3) | maskPattern
    const bits = QRUtil.getBCHTypeInfo(data)

    // 垂直方向
    for (let i = 0; i < 15; i++) {
      const mod = !test && ((bits >> i) & 1) == 1
      
      if (i < 6) {
        this.modules[i][8] = mod
      } else if (i < 8) {
        this.modules[i + 1][8] = mod
      } else {
        this.modules[this.moduleCount - 15 + i][8] = mod
      }
    }

    // 水平方向
    for (let i = 0; i < 15; i++) {
      const mod = !test && ((bits >> i) & 1) == 1
      
      if (i < 8) {
        this.modules[8][this.moduleCount - i - 1] = mod
      } else if (i < 9) {
        this.modules[8][15 - i - 1 + 1] = mod
      } else {
        this.modules[8][15 - i - 1] = mod
      }
    }

    // 固定模块
    this.modules[this.moduleCount - 8][8] = !test
  }

  mapData(data, maskPattern) {
    let inc = -1
    let row = this.moduleCount - 1
    let bitIndex = 7
    let byteIndex = 0

    for (let col = this.moduleCount - 1; col > 0; col -= 2) {
      if (col == 6) col--

      while (true) {
        for (let c = 0; c < 2; c++) {
          if (this.modules[row][col - c] == null) {
            let dark = false

            if (byteIndex < data.length) {
              dark = (((data[byteIndex] >>> bitIndex) & 1) == 1)
            }

            const mask = QRUtil.getMask(maskPattern, row, col - c)

            if (mask) {
              dark = !dark
            }

            this.modules[row][col - c] = dark
            bitIndex--

            if (bitIndex == -1) {
              byteIndex++
              bitIndex = 7
            }
          }
        }

        row += inc

        if (row < 0 || this.moduleCount <= row) {
          row -= inc
          inc = -inc
          break
        }
      }
    }
  }

  createData() {
    const buffer = new QRBitBuffer()
    
    for (let i = 0; i < this.dataList.length; i++) {
      const data = this.dataList[i]
      buffer.put(data.mode, 4)
      buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, 1))
      data.write(buffer)
    }

    // 计算最大数据容量
    let totalDataCount = 0
    const rsBlocks = QRRSBlock.getRSBlocks(1, this.errorCorrectLevel)
    
    for (let i = 0; i < rsBlocks.length; i++) {
      totalDataCount += rsBlocks[i].dataCount
    }

    if (buffer.getLengthInBits() > totalDataCount * 8) {
      throw new Error("code length overflow. (" + buffer.getLengthInBits() + ">" + totalDataCount * 8 + ")")
    }

    // 结束码
    if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {
      buffer.put(0, 4)
    }

    // 填充
    while (buffer.getLengthInBits() % 8 != 0) {
      buffer.putBit(false)
    }

    // 填充数据
    while (true) {
      if (buffer.getLengthInBits() >= totalDataCount * 8) {
        break
      }
      buffer.put(QRCode.PAD0, 8)

      if (buffer.getLengthInBits() >= totalDataCount * 8) {
        break
      }
      buffer.put(QRCode.PAD1, 8)
    }

    return this.createBytes(buffer, rsBlocks)
  }

  createBytes(buffer, rsBlocks) {
    let offset = 0
    let maxDcCount = 0
    let maxEcCount = 0
    const dcdata = new Array(rsBlocks.length)
    const ecdata = new Array(rsBlocks.length)

    for (let r = 0; r < rsBlocks.length; r++) {
      const dcCount = rsBlocks[r].dataCount
      const ecCount = rsBlocks[r].totalCount - dcCount

      maxDcCount = Math.max(maxDcCount, dcCount)
      maxEcCount = Math.max(maxEcCount, ecCount)

      dcdata[r] = new Array(dcCount)
      
      for (let i = 0; i < dcdata[r].length; i++) {
        dcdata[r][i] = 0xff & buffer.buffer[i + offset]
      }
      offset += dcCount

      const rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount)
      const rawPoly = new QRPolynomial(dcdata[r], rsPoly.getLength() - 1)
      const modPoly = rawPoly.mod(rsPoly)
      
      ecdata[r] = new Array(rsPoly.getLength() - 1)
      for (let i = 0; i < ecdata[r].length; i++) {
        const modIndex = i + modPoly.getLength() - ecdata[r].length
        ecdata[r][i] = (modIndex >= 0) ? modPoly.get(modIndex) : 0
      }
    }

    let totalCodeCount = 0
    for (let i = 0; i < rsBlocks.length; i++) {
      totalCodeCount += rsBlocks[i].totalCount
    }

    const data = new Array(totalCodeCount)
    let index = 0

    for (let i = 0; i < maxDcCount; i++) {
      for (let r = 0; r < rsBlocks.length; r++) {
        if (i < dcdata[r].length) {
          data[index++] = dcdata[r][i]
        }
      }
    }

    for (let i = 0; i < maxEcCount; i++) {
      for (let r = 0; r < rsBlocks.length; r++) {
        if (i < ecdata[r].length) {
          data[index++] = ecdata[r][i]
        }
      }
    }

    return data
  }

  getBestMaskPattern() {
    let minLostPoint = 0
    let pattern = 0

    for (let i = 0; i < 8; i++) {
      this.makeImpl(true, i)
      const lostPoint = QRUtil.getLostPoint(this)

      if (i == 0 || minLostPoint > lostPoint) {
        minLostPoint = lostPoint
        pattern = i
      }
    }

    return pattern
  }

  isDark(row, col) {
    if (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {
      throw new Error(row + "," + col)
    }
    return this.modules[row][col]
  }

  getModuleCount() {
    return this.moduleCount
  }
}

// 静态常量
QRCode.PAD0 = 0xEC
QRCode.PAD1 = 0x11

// 错误纠正级别
const QRErrorCorrectLevel = {
  L: 1,
  M: 0,
  Q: 3,
  H: 2
}

// 8位字节数据
class QR8bitByte {
  constructor(data) {
    this.mode = QRMode.MODE_8BIT_BYTE
    this.data = data
  }

  getLength() {
    return this.data.length
  }

  write(buffer) {
    for (let i = 0; i < this.data.length; i++) {
      buffer.put(this.data.charCodeAt(i), 8)
    }
  }
}

// 模式
const QRMode = {
  MODE_NUMBER: 1 << 0,
  MODE_ALPHA_NUM: 1 << 1,
  MODE_8BIT_BYTE: 1 << 2,
  MODE_KANJI: 1 << 3
}

// 位缓冲区
class QRBitBuffer {
  constructor() {
    this.buffer = []
    this.length = 0
  }

  get(index) {
    const bufIndex = Math.floor(index / 8)
    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) == 1
  }

  put(num, length) {
    for (let i = 0; i < length; i++) {
      this.putBit(((num >>> (length - i - 1)) & 1) == 1)
    }
  }

  getLengthInBits() {
    return this.length
  }

  putBit(bit) {
    const bufIndex = Math.floor(this.length / 8)
    if (this.buffer.length <= bufIndex) {
      this.buffer.push(0)
    }

    if (bit) {
      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))
    }

    this.length++
  }
}

// 工具类
const QRUtil = {
  getBCHTypeInfo: function(data) {
    let d = data << 10
    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(0x537) >= 0) {
      d ^= (0x537 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(0x537)))
    }
    return ((data << 10) | d) ^ 0x5412
  },

  getBCHDigit: function(data) {
    let digit = 0
    while (data != 0) {
      digit++
      data >>>= 1
    }
    return digit
  },

  getMask: function(maskPattern, i, j) {
    switch (maskPattern) {
      case 0: return (i + j) % 2 == 0
      case 1: return i % 2 == 0
      case 2: return j % 3 == 0
      case 3: return (i + j) % 3 == 0
      case 4: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0
      case 5: return (i * j) % 2 + (i * j) % 3 == 0
      case 6: return ((i * j) % 2 + (i * j) % 3) % 2 == 0
      case 7: return ((i * j) % 3 + (i + j) % 2) % 2 == 0
      default: throw new Error("bad maskPattern:" + maskPattern)
    }
  },

  getLengthInBits: function(mode, type) {
    if (1 <= type && type < 10) {
      switch(mode) {
        case QRMode.MODE_NUMBER: return 10
        case QRMode.MODE_ALPHA_NUM: return 9
        case QRMode.MODE_8BIT_BYTE: return 8
        case QRMode.MODE_KANJI: return 8
        default: throw new Error("mode:" + mode)
      }
    } else {
      throw new Error("type:" + type)
    }
  },

  getLostPoint: function(qrCode) {
    const moduleCount = qrCode.getModuleCount()
    let lostPoint = 0

    // 水平相同颜色的模块
    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        let sameCount = 0
        const dark = qrCode.isDark(row, col)

        for (let r = -1; r <= 1; r++) {
          if (row + r < 0 || moduleCount <= row + r) continue

          for (let c = -1; c <= 1; c++) {
            if (col + c < 0 || moduleCount <= col + c) continue
            if (r == 0 && c == 0) continue

            if (dark == qrCode.isDark(row + r, col + c)) {
              sameCount++
            }
          }
        }

        if (sameCount > 5) {
          lostPoint += (3 + sameCount - 5)
        }
      }
    }

    return lostPoint
  },

  getErrorCorrectPolynomial: function(errorCorrectLength) {
    let a = new QRPolynomial([1], 0)

    for (let i = 0; i < errorCorrectLength; i++) {
      a = a.multiply(new QRPolynomial([1, QRMath.gexp(i)], 0))
    }

    return a
  }
}

// RS块
const QRRSBlock = {
  getRSBlocks: function(typeNumber, errorCorrectLevel) {
    const rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel)
    
    if (rsBlock == undefined) {
      throw new Error("bad rs block @ typeNumber:" + typeNumber + "/errorCorrectLevel:" + errorCorrectLevel)
    }

    const length = rsBlock.length / 3
    const list = []

    for (let i = 0; i < length; i++) {
      const count = rsBlock[i * 3 + 0]
      const totalCount = rsBlock[i * 3 + 1]
      const dataCount = rsBlock[i * 3 + 2]

      for (let j = 0; j < count; j++) {
        list.push({totalCount: totalCount, dataCount: dataCount})
      }
    }

    return list
  },

  getRsBlockTable: function(typeNumber, errorCorrectLevel) {
    switch(errorCorrectLevel) {
      case QRErrorCorrectLevel.L:
        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0]
      case QRErrorCorrectLevel.M:
        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1]
      case QRErrorCorrectLevel.Q:
        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2]
      case QRErrorCorrectLevel.H:
        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3]
      default:
        return undefined
    }
  },

  RS_BLOCK_TABLE: [
    [1, 26, 19],
    [1, 26, 16],
    [1, 26, 13],
    [1, 26, 9]
  ]
}

// 多项式
class QRPolynomial {
  constructor(num, shift) {
    if (num.length == undefined) {
      throw new Error(num.length + "/" + shift)
    }

    let offset = 0

    while (offset < num.length && num[offset] == 0) {
      offset++
    }

    this.num = new Array(num.length - offset + shift)
    for (let i = 0; i < num.length - offset; i++) {
      this.num[i] = num[i + offset]
    }
  }

  get(index) {
    return this.num[index]
  }

  getLength() {
    return this.num.length
  }

  multiply(e) {
    const num = new Array(this.getLength() + e.getLength() - 1)

    for (let i = 0; i < this.getLength(); i++) {
      for (let j = 0; j < e.getLength(); j++) {
        num[i + j] ^= QRMath.glog(QRMath.gexp(this.get(i)) * QRMath.gexp(e.get(j)))
      }
    }

    return new QRPolynomial(num, 0)
  }

  mod(e) {
    if (this.getLength() - e.getLength() < 0) {
      return this
    }

    const ratio = QRMath.glog(this.get(0)) - QRMath.glog(e.get(0))
    const num = new Array(this.getLength())
    
    for (let i = 0; i < this.getLength(); i++) {
      num[i] = this.get(i)
    }

    for (let i = 0; i < e.getLength(); i++) {
      num[i] ^= QRMath.gexp(QRMath.glog(e.get(i)) + ratio)
    }

    return new QRPolynomial(num, 0).mod(e)
  }
}

// 数学工具
const QRMath = {
  glog: function(n) {
    if (n < 1) {
      throw new Error("glog(" + n + ")")
    }
    return QRMath.LOG_TABLE[n]
  },

  gexp: function(n) {
    while (n < 0) {
      n += 255
    }
    while (n >= 256) {
      n -= 255
    }
    return QRMath.EXP_TABLE[n]
  },

  EXP_TABLE: new Array(256),
  LOG_TABLE: new Array(256)
}

// 初始化数学表
for (let i = 0; i < 8; i++) {
  QRMath.EXP_TABLE[i] = 1 << i
}
for (let i = 8; i < 256; i++) {
  QRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4] ^ QRMath.EXP_TABLE[i - 5] ^ QRMath.EXP_TABLE[i - 6] ^ QRMath.EXP_TABLE[i - 8]
}
for (let i = 0; i < 255; i++) {
  QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]] = i
}

// 导出函数
export function generateQRCode(text, size = 120) {
  const qr = new QRCode()
  qr.addData(text)
  qr.make()
  
  return {
    modules: qr.modules,
    moduleCount: qr.getModuleCount(),
    size: size
  }
}

export function drawQRCode(canvasId, qrData, componentInstance) {
  const ctx = uni.createCanvasContext(canvasId, componentInstance)
  const moduleCount = qrData.moduleCount
  const cellSize = qrData.size / moduleCount
  
  // 清空画布
  ctx.clearRect(0, 0, qrData.size, qrData.size)
  
  // 绘制白色背景
  ctx.setFillStyle('#ffffff')
  ctx.fillRect(0, 0, qrData.size, qrData.size)
  
  // 绘制二维码模块
  ctx.setFillStyle('#000000')
  for (let row = 0; row < moduleCount; row++) {
    for (let col = 0; col < moduleCount; col++) {
      if (qrData.modules[row][col]) {
        ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize)
      }
    }
  }
  
  ctx.draw()
}
