<template>
  <view class="poster-container">
    <view class="case-detail" v-if="caseInfo">
      <!-- 用户信息 -->
      <view class="user-section">
        <image class="user-avatar" :src="processAvatarUrl(caseInfo.publisher.avatar)" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{ caseInfo.publisher.nickName }}</text>
          <text class="publish-time">{{ formatTime(caseInfo.createTime) }}</text>
        </view>
      </view>

      <!-- 案例标签 -->
      <view class="case-tags" v-if="caseInfo.caseTags">
        <text class="tag" v-for="tag in processTags(caseInfo.caseTags)" :key="tag">#{{ tag }}</text>
      </view>

      <!-- 案例标题 -->
      <view class="case-title">{{ caseInfo.caseTitle }}</view>

      <!-- 案例内容 -->
      <view class="case-content">
        <rich-text :nodes="caseInfo.caseContent"></rich-text>
      </view>

      <!-- 案例图片 -->
      <view class="case-images" v-if="caseInfo.caseImages">
        <view class="image-list">
          <view class="image-item" v-for="(image, index) in processImages(caseInfo.caseImages)" :key="index">
            <image class="case-image" :src="image" mode="widthFix"></image>
          </view>
        </view>
      </view>

      <!-- 点击统计 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-label">{{ caseInfo.templateType }}</text>
          <text class="stats-label">阅读{{ caseInfo.clickCount }}次</text>
        </view>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-title">扫码查看详情</view>
        <canvas canvas-id="qrcode" class="qrcode-canvas"
          :style="{ width: qrcodeSize + 'px', height: qrcodeSize + 'px' }"></canvas>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-else>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import { caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags } from '@/utils/utils.js'
import { generateQRCode, drawQRCode } from '@/utils/qrcode.js'

export default {
  data() {
    return {
      caseId: null,
      caseInfo: null,
      qrcodeSize: 120,
      qrcodeUrl: ''
    }
  },

  onLoad(options) {
    this.caseId = options.caseId
    if (this.caseId) {
      this.loadCaseDetail()
      this.generateQRCodeUrl()
    }
  },

  methods: {
    // 加载案例详情
    async loadCaseDetail() {
      try {
        const res = await caseInfoApi.getCaseDetail(this.caseId)
        this.caseInfo = res.data

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: '案例海报'
        })

        // 生成二维码
        this.$nextTick(() => {
          this.generateQRCode()
        })
      } catch (error) {
        console.error('加载案例详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 生成二维码URL
    generateQRCodeUrl() {
      // 构建案例详情页的完整URL
      // #ifdef H5
      this.qrcodeUrl = `${window.location.origin}/#/pages/case/detail?caseId=${this.caseId}`
      // #endif

      // #ifdef MP-WEIXIN
      this.qrcodeUrl = `pages/case/detail?caseId=${this.caseId}`
      // #endif

      // #ifdef APP-PLUS
      this.qrcodeUrl = `https://your-domain.com/pages/case/detail?caseId=${this.caseId}`
      // #endif

      console.log('生成的二维码URL:', this.qrcodeUrl)
    },

    // 生成二维码
    generateQRCode() {
      if (!this.qrcodeUrl) return

      try {
        // 使用专业的二维码生成库
        const qrData = generateQRCode(this.qrcodeUrl, this.qrcodeSize)
        drawQRCode('qrcode', qrData, this)
      } catch (error) {
        console.error('生成二维码失败:', error)
        // 如果专业库失败，使用简化版本
        this.generateSimpleQRCode()
      }
    },

    // 简化版二维码生成（备用方案）
    generateSimpleQRCode() {
      const ctx = uni.createCanvasContext('qrcode', this)

      // 绘制二维码背景
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(0, 0, this.qrcodeSize, this.qrcodeSize)

      // 绘制简单的二维码样式
      ctx.setFillStyle('#000000')
      const cellSize = this.qrcodeSize / 21 // 21x21 网格

      // 绘制定位标记
      this.drawPositionMarker(ctx, 0, 0, cellSize)
      this.drawPositionMarker(ctx, 14 * cellSize, 0, cellSize)
      this.drawPositionMarker(ctx, 0, 14 * cellSize, cellSize)

      // 绘制数据点（简化版本）
      for (let i = 0; i < 21; i++) {
        for (let j = 0; j < 21; j++) {
          if (this.shouldDrawCell(i, j)) {
            ctx.fillRect(i * cellSize, j * cellSize, cellSize, cellSize)
          }
        }
      }

      ctx.draw()
    },

    // 绘制定位标记
    drawPositionMarker(ctx, x, y, cellSize) {
      // 外框
      ctx.fillRect(x, y, 7 * cellSize, 7 * cellSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(x + cellSize, y + cellSize, 5 * cellSize, 5 * cellSize)
      ctx.setFillStyle('#000000')
      ctx.fillRect(x + 2 * cellSize, y + 2 * cellSize, 3 * cellSize, 3 * cellSize)
    },

    // 判断是否应该绘制单元格（简化逻辑）
    shouldDrawCell(i, j) {
      // 避开定位标记区域
      if ((i < 9 && j < 9) || (i > 12 && j < 9) || (i < 9 && j > 12)) {
        return false
      }

      // 简单的伪随机模式（实际应根据数据生成）
      return (i + j + parseInt(this.caseId)) % 3 === 0
    },

    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags
  }
}
</script>

<style scoped>
.poster-container {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 0;
}

.case-detail {
  background-color: #fff;
  padding: 30rpx;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 26rpx;
  color: #999;
}

/* 案例标签 */
.case-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #1890ff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 案例标题 */
.case-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 案例内容 */
.case-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

/* 案例图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.image-item {
  width: 100%;
}

.case-image {
  width: 100%;
  border-radius: 12rpx;
}

/* 统计信息 */
.stats-section {
  margin-bottom: 40rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 28rpx;
  color: #999;
}

/* 二维码区域 */
.qrcode-section {
  text-align: center;
  padding: 40rpx 0;
  border-top: 2rpx solid #f0f0f0;
}

.qrcode-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.qrcode-canvas {
  margin: 0 auto;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
</style>
