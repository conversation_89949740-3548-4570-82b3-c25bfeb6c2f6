package com.ruoyi.system.service;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 海报生成服务
 * 
 * <AUTHOR>
 */
@Service
public class PosterGenerationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PosterGenerationService.class);
    
    /**
     * 生成案例海报
     *
     * @param caseId 案例ID
     * @param request HTTP请求对象
     * @return 海报图片URL
     * @throws Exception 生成异常
     */
    public String generateCasePoster(Long caseId, HttpServletRequest request) throws Exception {
        try {
            logger.info("开始生成案例海报 - 案例ID: {}", caseId);

            // 构建海报页面URL
            String baseUrl = getBaseUrl(request);
            String posterPageUrl = baseUrl + "/#/pages/case/poster?caseId=" + caseId;
            logger.info("海报页面URL: {}", posterPageUrl);

            // 尝试调用Node.js海报生成服务
            try {
                String nodeServiceUrl = "http://localhost:3001/api/generate-poster";
                String posterUrl = callPosterService(nodeServiceUrl, posterPageUrl, caseId);
                if (posterUrl != null) {
                    logger.info("通过Node.js服务生成海报成功 - 案例ID: {}, URL: {}", caseId, posterUrl);
                    return posterUrl;
                }
            } catch (Exception e) {
                logger.warn("Node.js海报服务调用失败，使用备用方案: {}", e.getMessage());
            }

            // 备用方案：创建示例海报
            String fileName = generateFileName(caseId);
            String filePath = createSamplePosterImage(fileName, caseId);
            String accessUrl = "/poster" + filePath.substring(filePath.indexOf("/poster") + 7);

            logger.info("海报生成成功（备用方案） - 案例ID: {}, 文件路径: {}, 访问URL: {}", caseId, filePath, accessUrl);
            return accessUrl;

        } catch (Exception e) {
            logger.error("生成案例海报失败 - 案例ID: {}", caseId, e);
            throw new Exception("海报生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用Node.js海报生成服务
     */
    private String callPosterService(String serviceUrl, String pageUrl, Long caseId) throws Exception {
        // 这里应该实现HTTP调用Node.js服务的逻辑
        // 由于若依框架可能没有现成的HTTP客户端工具，这里返回null表示调用失败
        // 在实际项目中，可以使用RestTemplate、OkHttp或其他HTTP客户端
        logger.info("尝试调用海报生成服务: {}", serviceUrl);
        return null;
    }
    
    /**
     * 获取基础URL
     */
    private String getBaseUrl(HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        
        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);
        
        if ((scheme.equals("http") && serverPort != 80) || 
            (scheme.equals("https") && serverPort != 443)) {
            url.append(":").append(serverPort);
        }
        
        return url.toString();
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(Long caseId) {
        String datePath = DateUtils.datePath();
        String timestamp = String.valueOf(System.currentTimeMillis());
        return datePath + "/poster_case_" + caseId + "_" + timestamp + ".png";
    }
    
    /**
     * 创建示例海报图片
     */
    private String createSamplePosterImage(String fileName, Long caseId) throws IOException {
        // 创建保存目录
        String uploadPath = RuoYiConfig.getProfile() + "/poster";
        String fullPath = uploadPath + "/" + fileName;

        File file = new File(fullPath);
        File parentDir = file.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 创建一个简单的示例图片内容（实际项目中应该是真实的截图）
        String sampleContent = "案例海报 - 案例ID: " + caseId + "\n生成时间: " + DateUtils.getTime();

        try (FileOutputStream fos = new FileOutputStream(file)) {
            // 这里应该写入真实的图片数据，现在只是写入文本作为示例
            fos.write(sampleContent.getBytes("UTF-8"));
        }

        logger.info("示例海报文件已创建: {}", fullPath);
        return fullPath;
    }
}
