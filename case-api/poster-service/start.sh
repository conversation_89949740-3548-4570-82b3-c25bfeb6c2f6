#!/bin/bash

# 海报生成服务启动脚本

echo "正在启动案例海报生成服务..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到npm，请先安装npm"
    exit 1
fi

# 进入服务目录
cd "$(dirname "$0")"

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo "错误: 未找到package.json文件"
    exit 1
fi

# 安装依赖
echo "正在安装依赖..."
npm install

# 检查安装是否成功
if [ $? -ne 0 ]; then
    echo "错误: 依赖安装失败"
    exit 1
fi

# 创建上传目录
UPLOAD_DIR="/Users/<USER>/uploadPath/poster"
if [ ! -d "$UPLOAD_DIR" ]; then
    echo "创建上传目录: $UPLOAD_DIR"
    mkdir -p "$UPLOAD_DIR"
fi

# 启动服务
echo "正在启动海报生成服务..."
echo "服务将在端口3001上运行"
echo "按Ctrl+C停止服务"
echo ""

# 设置环境变量并启动
export UPLOAD_DIR="$UPLOAD_DIR"
npm start
