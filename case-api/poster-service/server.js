const express = require("express");
const puppeteer = require("puppeteer");
const cors = require("cors");
const fs = require("fs");
const path = require("path");

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 配置上传目录
const UPLOAD_DIR = process.env.UPLOAD_DIR || "/Users/<USER>/uploadPath/poster";

// 确保上传目录存在
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * 生成海报接口
 */
app.post("/api/generate-poster", async (req, res) => {
  let browser = null;

  try {
    const { url, caseId, width = 1200, height = 1600 } = req.body;

    if (!url || !caseId) {
      return res.status(400).json({
        success: false,
        message: "缺少必要参数: url 和 caseId",
      });
    }

    console.log(`开始生成海报 - 案例ID: ${caseId}, URL: ${url}`);

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
      ],
      timeout: 30000,
      protocolTimeout: 30000,
    });

    const page = await browser.newPage();

    // 设置视口大小
    await page.setViewport({
      width: parseInt(width),
      height: parseInt(height),
      deviceScaleFactor: 2, // 高清截图
    });

    // 设置用户代理
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    );

    // 访问页面
    console.log(`正在访问页面: ${url}`);
    await page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // 等待页面内容加载完成
    try {
      await page.waitForSelector(".case-title", { timeout: 10000 });
      console.log("页面内容加载完成");
    } catch (error) {
      console.warn("等待页面内容超时，继续截图");
    }

    // 额外等待确保二维码等动态内容生成完成
    await page.waitForTimeout(3000);

    // 生成文件名
    const timestamp = Date.now();
    const fileName = `poster_case_${caseId}_${timestamp}.png`;
    const filePath = path.join(UPLOAD_DIR, fileName);

    // 截取整个页面
    console.log("开始截取页面截图...");
    await page.screenshot({
      path: filePath,
      fullPage: true,
      type: "png",
      quality: 90,
    });

    console.log(`海报生成成功: ${filePath}`);

    // 返回结果
    res.json({
      success: true,
      message: "海报生成成功",
      data: {
        fileName: fileName,
        filePath: `/poster/${fileName}`,
        caseId: caseId,
      },
    });
  } catch (error) {
    console.error("生成海报失败:", error);
    res.status(500).json({
      success: false,
      message: "海报生成失败: " + error.message,
    });
  } finally {
    // 关闭浏览器
    if (browser) {
      try {
        await browser.close();
      } catch (error) {
        console.warn("关闭浏览器失败:", error.message);
      }
    }
  }
});

/**
 * 健康检查接口
 */
app.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "海报生成服务运行正常",
    timestamp: new Date().toISOString(),
  });
});

/**
 * 获取服务信息
 */
app.get("/api/info", (req, res) => {
  res.json({
    name: "Case Poster Service",
    version: "1.0.0",
    description: "案例海报生成服务",
    endpoints: {
      "POST /api/generate-poster": "生成海报",
      "GET /health": "健康检查",
      "GET /api/info": "服务信息",
    },
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error("服务器错误:", error);
  res.status(500).json({
    success: false,
    message: "服务器内部错误",
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "接口不存在",
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`海报生成服务已启动，端口: ${PORT}`);
  console.log(`上传目录: ${UPLOAD_DIR}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`服务信息: http://localhost:${PORT}/api/info`);
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在关闭服务...");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在关闭服务...");
  process.exit(0);
});
